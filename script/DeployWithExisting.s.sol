// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Script.sol";
import "src/Subscription.sol";
import "src/contracts/SubscriptionLogicV1.sol";
import "src/contracts/SubscriptionLogicV2.sol";

contract DeployWithExistingScript is Script {
    // Set to true to use existing deployed contracts
    bool constant USE_EXISTING_LOGIC = true;

    // Already deployed contract addresses on Hedera testnet
    address constant EXISTING_LOGIC_V1 = 0xAEC620bb35c403E8b87296b5F4AC49699B45C1c1;
    address constant EXISTING_LOGIC_V2 = 0x19fDAFe65dacDc594296a859632CB3eE64Efa7cC;

    function run() external returns (Subscription proxy, SubscriptionLogicV1 logicV1, SubscriptionLogicV2 logicV2) {
        uint256 deployerPrivateKey = vm.envUint("OPERATOR_KEY");
        address deployer = vm.addr(deployerPrivateKey);

        console.log("Deployer address:", deployer);

        vm.startBroadcast();

        if (USE_EXISTING_LOGIC) {
            // Use existing logic contracts
            logicV1 = SubscriptionLogicV1(EXISTING_LOGIC_V1);
            logicV2 = SubscriptionLogicV2(EXISTING_LOGIC_V2);
            console.log("Using existing SubscriptionLogicV1 at:", address(logicV1));
            console.log("Using existing SubscriptionLogicV2 at:", address(logicV2));
        } else {
            // Deploy new logic contracts
            logicV1 = new SubscriptionLogicV1();
            console.log("Deployed SubscriptionLogicV1 at:", address(logicV1));

            logicV2 = new SubscriptionLogicV2();
            console.log("Deployed SubscriptionLogicV2 at:", address(logicV2));
        }

        // Deploy the proxy contract
        proxy = new Subscription(EXISTING_LOGIC_V1);
        console.log("Deployed Subscription Proxy at:", address(proxy));

        vm.stopBroadcast();

        // Log all addresses for reference
        console.log("\n=== DEPLOYMENT SUMMARY ===");
        console.log("Proxy Address:", address(proxy));
        console.log("LogicV1 Address:", address(logicV1));
        console.log("LogicV2 Address:", address(logicV2));
        console.log("Owner Address:", deployer);

        return (proxy, logicV1, logicV2);
    }
}
