// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import {Script, console} from "forge-std/Script.sol";
import {Subscription} from "src/Subscription.sol";
import {SubscriptionLogicV1} from "src/contracts/SubscriptionLogicV1.sol";
import {SubscriptionLogicV2} from "src/contracts/SubscriptionLogicV2.sol";
import {LibSubscription} from "src/LibSubscription.sol";

contract DeployScript is Script {
    Subscription public proxy;
    SubscriptionLogicV1 public logicV1;
    SubscriptionLogicV2 public logicV2;

    function run()
        public
        returns (Subscription, SubscriptionLogicV1, SubscriptionLogicV2)
    {
        vm.startBroadcast();

        // Deploy Logic V1
        logicV1 = new SubscriptionLogicV1();
        console.log("Deployed SubscriptionLogicV1 at:", address(logicV1));

        // Deploy Logic V2
        logicV2 = new SubscriptionLogicV2();
        console.log("Deployed SubscriptionLogicV2 at:", address(logicV2));

        // Deploy Proxy and initialize with Logic V1
        proxy = new Subscription(address(logicV1));
        console.log("Deployed Subscription Proxy at:", address(proxy));
        console.log("Proxy initialized with Logic V1.");

        vm.stopBroadcast();

        return (proxy, logicV1, logicV2);
    }
}
