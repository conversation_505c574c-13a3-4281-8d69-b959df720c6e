// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Script.sol";
import "src/Subscription.sol";
import "src/contracts/SubscriptionLogicV1.sol";
import "src/contracts/SubscriptionLogicV2.sol";

contract DeployProxyOnlyScript is Script {
    // Already deployed contract addresses on Hedera testnet
    address constant LOGIC_V1_ADDRESS = 0xAEC620bb35c403E8b87296b5F4AC49699B45C1c1;
    address constant LOGIC_V2_ADDRESS = 0x19fDAFe65dacDc594296a859632CB3eE64Efa7cC;

    function run() external returns (Subscription proxy) {
        console.log("Using existing SubscriptionLogicV1 at:", LOGIC_V1_ADDRESS);
        console.log("Using existing SubscriptionLogicV2 at:", LOGIC_V2_ADDRESS);

        vm.startBroadcast();

        // Deploy only the proxy contract
        proxy = new Subscription(LOGIC_V1_ADDRESS);
        console.log("Deployed Subscription Proxy at:", address(proxy));

        vm.stopBroadcast();

        // Log all addresses for reference
        console.log("\n=== DEPLOYMENT SUMMARY ===");
        console.log("Proxy Address:", address(proxy));
        console.log("LogicV1 Address:", LOGIC_V1_ADDRESS);
        console.log("LogicV2 Address:", LOGIC_V2_ADDRESS);

        return proxy;
    }
}
