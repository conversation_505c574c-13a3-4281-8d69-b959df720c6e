[profile.default]
src = "src"
out = "out"
libs = ["lib"]

[profile.hedera]
src = "src"
out = "out"
libs = ["lib"]
# Hedera-specific settings
gas_limit = 30000000
gas_price = 660000000001  # 660 gwei + 1 wei

[rpc_endpoints]
testnet = "${SEPOLIA_RPC_URL}"
h_testnet = "${HEDERA_TESTNET_RPC_URL}"

# See more config options https://github.com/foundry-rs/foundry/blob/master/crates/config/README.md#all-options
