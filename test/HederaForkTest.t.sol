// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import "src/Subscription.sol";
import "src/contracts/SubscriptionLogicV1.sol";
import "src/contracts/SubscriptionLogicV2.sol";

contract HederaForkTest is Test {
    // Your deployed contract addresses on Hedera testnet
    Subscription proxy = Subscription(payable(******************************************));
    SubscriptionLogicV1 logicV1 = SubscriptionLogicV1(******************************************);
    SubscriptionLogicV2 logicV2 = SubscriptionLogicV2(******************************************);

    address OWNER; // Will be set dynamically from the actual contract
    address constant USER1 = address(0x1);
    address constant USER2 = address(0x2);

    uint256 constant PLAN_PRICE_BASIC = 1 ether;
    uint256 constant PLAN_DURATION_BASIC = 30 days;

    function setUp() public {
        // Get the actual owner from the deployed contract
        OWNER = proxy.proxyOwner();

        // Fork tests run against the actual deployed state
        vm.label(address(proxy), "Subscription Proxy");
        vm.label(address(logicV1), "Logic V1");
        vm.label(address(logicV2), "Logic V2");
        vm.label(OWNER, "Owner");

        console.log("Actual contract owner:", OWNER);
    }

    function getV1Proxy() internal view returns (SubscriptionLogicV1) {
        return SubscriptionLogicV1(address(proxy));
    }

    function getV2Proxy() internal view returns (SubscriptionLogicV2) {
        return SubscriptionLogicV2(address(proxy));
    }

    function test_DeployedContractsWork() public {
        // Test that deployed contracts are functional
        assertEq(proxy.getLogicContract(), address(logicV1));
        assertEq(proxy.proxyOwner(), OWNER);

        // Test creating a plan
        vm.startPrank(OWNER);
        uint8 planId = getV1Proxy().createPlan("Test Plan", PLAN_PRICE_BASIC, PLAN_DURATION_BASIC);
        vm.stopPrank();

        assertTrue(getV1Proxy().planExists(planId));
        assertEq(getV1Proxy().getActivePlansCount(), 1);
    }

    function test_SubscriptionOnDeployedContract() public {
        // Create a plan first
        vm.startPrank(OWNER);
        uint8 planId = getV1Proxy().createPlan("Fork Test Plan", PLAN_PRICE_BASIC, PLAN_DURATION_BASIC);
        vm.stopPrank();

        // Test subscription
        vm.deal(USER1, PLAN_PRICE_BASIC);
        vm.startPrank(USER1);
        getV1Proxy().subscribe{value: PLAN_PRICE_BASIC}(planId);
        vm.stopPrank();

        assertTrue(getV1Proxy().isActiveSubscriber(USER1));
    }

    function test_UpgradeOnDeployedContract() public {
        // Check current logic contract
        address currentLogic = proxy.getLogicContract();
        console.log("Current logic contract:", currentLogic);
        console.log("LogicV1 address:", address(logicV1));
        console.log("LogicV2 address:", address(logicV2));

        // Only upgrade if not already on V2
        if (currentLogic != address(logicV2)) {
            vm.startPrank(OWNER);
            proxy.upgradeLogic(address(logicV2));
            vm.stopPrank();

            assertEq(proxy.getLogicContract(), address(logicV2));
        }

        // Test V2 functionality works regardless of upgrade
        vm.startPrank(OWNER);
        uint8 planId = getV2Proxy().createPlan("V2 Plan", PLAN_PRICE_BASIC, PLAN_DURATION_BASIC);
        vm.stopPrank();

        assertTrue(getV2Proxy().planExists(planId));
    }
}
