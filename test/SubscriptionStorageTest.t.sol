// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import {Test, console} from "forge-std/Test.sol";
import {DeployScript} from "script/DeploySubscription.s.sol";
import {Subscription} from "src/Subscription.sol";
import {SubscriptionLogicV1} from "src/contracts/SubscriptionLogicV1.sol";
import {SubscriptionLogicV2} from "src/contracts/SubscriptionLogicV2.sol";
import {LibSubscription} from "src/LibSubscription.sol";

contract SubscriptionStorageTest is Test {
    Subscription public proxy;
    SubscriptionLogicV1 public logicV1;
    SubscriptionLogicV2 public logicV2;

    address public OWNER;
    address public USER1 = makeAddr("user1");
    address public USER2 = makeAddr("user2");
    address public USER3 = makeAddr("user3");
    address public USER4 = makeAddr("user4");
    address public USER5 = makeAddr("user5");

    // Plan data
    uint256 public constant PLAN_PRICE_BASIC = 1 ether;
    uint256 public constant PLAN_DURATION_BASIC = 30 days;
    uint256 public constant PLAN_PRICE_PRO = 2 ether;
    uint256 public constant PLAN_DURATION_PRO = 60 days;

    // Structs to avoid stack too deep
    struct PlanIds {
        uint8 basicPlanId;
        uint8 proPlanId;
        uint8 premiumPlanId;
        uint8 deletedPlanId;
    }

    struct UserSubscriptionState {
        uint8 planId;
        uint256 expiry;
        bool paused;
        bool active;
    }

    function setUp() public virtual {
        // Deploy contracts using the script
        (proxy, logicV1, logicV2) = new DeployScript().run();

        OWNER = proxy.proxyOwner();

        console.log("Test Setup Complete:");
        console.log("  Proxy Address:", address(proxy));
        console.log("  LogicV1 Address:", address(logicV1));
        console.log("  LogicV2 Address:", address(logicV2));
        console.log("  Owner Address:", OWNER);
    }

    // --- Helper function to interact with the proxy as a specific logic version ---
    function getV1Proxy() internal view returns (SubscriptionLogicV1) {
        return SubscriptionLogicV1(address(proxy));
    }

    function getV2Proxy() internal view returns (SubscriptionLogicV2) {
        return SubscriptionLogicV2(address(proxy));
    }

    // ============ FOCUSED TESTS ============

    function test_MultipleUsersIndependentSubscriptions() public {
        PlanIds memory planIds = _createBasicPlans();

        // Setup subscriptions for different users
        _subscribeUser(USER1, planIds.basicPlanId, PLAN_PRICE_BASIC);
        _subscribeUser(USER2, planIds.proPlanId, PLAN_PRICE_PRO);

        // Verify independence
        assertTrue(getV1Proxy().isActiveSubscriber(USER1));
        assertTrue(getV1Proxy().isActiveSubscriber(USER2));

        // Expire USER1's subscription
        vm.warp(block.timestamp + PLAN_DURATION_BASIC + 1);

        assertFalse(getV1Proxy().isActiveSubscriber(USER1));
        assertTrue(getV1Proxy().isActiveSubscriber(USER2)); // Should still be active
    }

    function test_OverpaymentRefund() public {
        PlanIds memory planIds = _createBasicPlans();
        uint256 overpayment = 0.5 ether;

        vm.deal(USER1, PLAN_PRICE_BASIC + overpayment);
        vm.startPrank(USER1);
        getV1Proxy().subscribe{value: PLAN_PRICE_BASIC + overpayment}(
            planIds.basicPlanId
        );

        uint256 balanceBefore = USER1.balance;
        getV1Proxy().withdrawRefund();
        vm.stopPrank();

        assertEq(USER1.balance, balanceBefore + overpayment);
    }

    // ============ FUZZ TESTS ============

    function testFuzz_SubscriptionWithRandomOverpayment(
        uint256 overpayment
    ) public {
        // Bound overpayment to reasonable range (0 to 10 ether)
        overpayment = bound(overpayment, 0, 10 ether);

        PlanIds memory planIds = _createBasicPlans();
        uint256 totalPayment = PLAN_PRICE_BASIC + overpayment;

        vm.deal(USER1, totalPayment);
        vm.startPrank(USER1);
        getV1Proxy().subscribe{value: totalPayment}(planIds.basicPlanId);

        // Should be active regardless of overpayment
        assertTrue(getV1Proxy().isActiveSubscriber(USER1));

        if (overpayment > 0) {
            uint256 balanceBefore = USER1.balance;
            getV1Proxy().withdrawRefund();
            assertEq(USER1.balance, balanceBefore + overpayment);
        }
        vm.stopPrank();
    }

    function testFuzz_MultipleUsersRandomPlans(
        uint8 userCount,
        uint8 planChoice
    ) public {
        // Bound inputs to reasonable ranges
        userCount = uint8(bound(userCount, 1, 5));
        planChoice = uint8(bound(planChoice, 0, 1)); // 0 = basic, 1 = pro

        PlanIds memory planIds = _createBasicPlans();
        address[] memory users = new address[](userCount);

        // Create users and subscribe them
        for (uint8 i = 0; i < userCount; i++) {
            users[i] = makeAddr(string(abi.encodePacked("fuzzUser", i)));

            uint8 selectedPlan = planChoice == 0
                ? planIds.basicPlanId
                : planIds.proPlanId;
            uint256 price = planChoice == 0 ? PLAN_PRICE_BASIC : PLAN_PRICE_PRO;

            _subscribeUser(users[i], selectedPlan, price);
            assertTrue(getV1Proxy().isActiveSubscriber(users[i]));
        }

        // Verify all users are independent
        assertEq(getV1Proxy().getActivePlansCount(), 2); // Should still be 2 plans
    }

    // ============ UPGRADE TESTS ============

    function test_UpgradePreservesBasicState() public {
        // Setup basic state
        PlanIds memory planIds = _createBasicPlans();
        _subscribeUser(USER1, planIds.basicPlanId, PLAN_PRICE_BASIC);
        _subscribeUser(USER2, planIds.proPlanId, PLAN_PRICE_PRO);

        // Capture pre-upgrade state
        uint8 preActivePlansCount = getV1Proxy().getActivePlansCount();
        bool preUser1Active = getV1Proxy().isActiveSubscriber(USER1);
        bool preUser2Active = getV1Proxy().isActiveSubscriber(USER2);

        // Upgrade to V2
        vm.startPrank(OWNER);
        proxy.upgradeLogic(address(logicV2));
        vm.stopPrank();

        // Verify state preservation
        assertEq(proxy.getLogicContract(), address(logicV2));
        assertEq(getV2Proxy().getActivePlansCount(), preActivePlansCount);
        assertEq(getV2Proxy().isActiveSubscriber(USER1), preUser1Active);
        assertEq(getV2Proxy().isActiveSubscriber(USER2), preUser2Active);

        // Test V2 functionality works
        vm.startPrank(OWNER);
        getV2Proxy().pauseUserSubscription(USER1);
        vm.stopPrank();

        assertFalse(getV2Proxy().isActiveSubscriber(USER1));
        assertTrue(getV2Proxy().isActiveSubscriber(USER2)); // Should still be active
    }

    function test_UpgradeWithRefundPreservation() public {
        // Test that refunds are preserved across upgrades
        PlanIds memory planIds = _createBasicPlans();
        uint256 overpayment = 0.3 ether;

        // Subscribe with overpayment
        vm.deal(USER1, PLAN_PRICE_BASIC + overpayment);
        vm.startPrank(USER1);
        getV1Proxy().subscribe{value: PLAN_PRICE_BASIC + overpayment}(
            planIds.basicPlanId
        );
        vm.stopPrank();

        // Upgrade to V2
        vm.startPrank(OWNER);
        proxy.upgradeLogic(address(logicV2));
        vm.stopPrank();

        // Verify refund still works in V2
        uint256 balanceBefore = USER1.balance;
        vm.startPrank(USER1);
        getV2Proxy().withdrawRefund();
        vm.stopPrank();

        assertEq(USER1.balance, balanceBefore + overpayment);
    }

    // ============ HELPER FUNCTIONS ============

    function _createBasicPlans() internal returns (PlanIds memory planIds) {
        vm.startPrank(OWNER);
        planIds.basicPlanId = getV1Proxy().createPlan(
            "Basic Plan",
            PLAN_PRICE_BASIC,
            PLAN_DURATION_BASIC
        );
        planIds.proPlanId = getV1Proxy().createPlan(
            "Pro Plan",
            PLAN_PRICE_PRO,
            PLAN_DURATION_PRO
        );
        vm.stopPrank();
    }

    function _subscribeUser(
        address user,
        uint8 planId,
        uint256 price
    ) internal {
        vm.deal(user, price);
        vm.startPrank(user);
        getV1Proxy().subscribe{value: price}(planId);
        vm.stopPrank();
    }
}
