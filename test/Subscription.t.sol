// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import {Test, console} from "forge-std/Test.sol";
import {DeployScript} from "script/DeploySubscription.s.sol";
import {Subscription} from "src/Subscription.sol";
import {SubscriptionLogicV1} from "src/contracts/SubscriptionLogicV1.sol";
import {SubscriptionLogicV2} from "src/contracts/SubscriptionLogicV2.sol";
import {LibSubscription} from "src/LibSubscription.sol"; // For AppStorage and Plan struct

contract SubscriptionTest is Test {
    Subscription public proxy;
    SubscriptionLogicV1 public logicV1;
    SubscriptionLogicV2 public logicV2;

    address public OWNER;
    address public USER1;
    address public USER2;
    address public NON_OWNER;

    // Plan data
    string public PLAN_NAME_BASIC = "Basic Plan";
    uint256 public PLAN_PRICE_BASIC = 1 ether;
    uint256 public PLAN_DURATION_BASIC = 30 days; // In seconds

    string public PLAN_NAME_PRO = "Pro Plan";
    uint256 public PLAN_PRICE_PRO = 2 ether;
    uint256 public PLAN_DURATION_PRO = 60 days;

    event PlanCreated(
        uint8 indexed planId,
        string name,
        uint256 price,
        uint256 duration
    );

    function setUp() public virtual {
        // Deploy contracts using the script
        (proxy, logicV1, logicV2) = new DeployScript().run();

        OWNER = proxy.proxyOwner();

        USER1 = makeAddr("user1");
        USER2 = makeAddr("user2");
        NON_OWNER = makeAddr("non_owner");

        console.log("Test Setup Complete:");
        console.log("  Proxy Address:", address(proxy));
        console.log("  LogicV1 Address:", address(logicV1));
        console.log("  LogicV2 Address:", address(logicV2));
        console.log("  Owner Address:", OWNER);
    }

    // --- Helper function to interact with the proxy as a specific logic version ---
    function getV1Proxy() internal view returns (SubscriptionLogicV1) {
        return SubscriptionLogicV1(address(proxy));
    }

    function getV2Proxy() internal view returns (SubscriptionLogicV2) {
        return SubscriptionLogicV2(address(proxy));
    }

    // --- Tests for V1 Functionality ---

    function test_V1_InitialState() public view {
        assertEq(proxy.getLogicContract(), address(logicV1));
        assertEq(proxy.proxyOwner(), OWNER);
        assertEq(getV1Proxy().getActivePlansCount(), 0);
    }

    function test_V1_CreatePlan() public {
        vm.startPrank(OWNER);

        // Test event emission
        vm.expectEmit(true, true, true, true);
        emit LibSubscription.PlanCreated(
            1,
            PLAN_NAME_BASIC,
            PLAN_PRICE_BASIC,
            PLAN_DURATION_BASIC
        );

        // Create a plan
        uint8 planId = getV1Proxy().createPlan(
            PLAN_NAME_BASIC,
            PLAN_PRICE_BASIC,
            PLAN_DURATION_BASIC
        );
        vm.stopPrank();

        assertEq(planId, 1);
        (
            LibSubscription.Plan[] memory activePlans,
            uint8[] memory planIds
        ) = getV1Proxy().getOnlyActivePlans();
        assertEq(activePlans.length, 1);
        assertEq(activePlans[0].name, PLAN_NAME_BASIC);
        assertEq(activePlans[0].price, PLAN_PRICE_BASIC);
        assertEq(activePlans[0].duration, PLAN_DURATION_BASIC);
        assertTrue(
            getV1Proxy().planExists(planId),
            "Plan should exist after creation"
        );
        assertEq(planIds[0], planId);
        assertEq(planIds.length, 1);
    }

    function test_V1_CreatePlan_NonOwnerReverts() public {
        vm.startPrank(NON_OWNER);
        vm.expectRevert(
            abi.encodeWithSelector(
                LibSubscription.NotOwner.selector,
                NON_OWNER,
                OWNER
            )
        );
        getV1Proxy().createPlan(
            PLAN_NAME_BASIC,
            PLAN_PRICE_BASIC,
            PLAN_DURATION_BASIC
        );
        vm.stopPrank();
    }

    function test_V1_CreatePlan_DuplicateNameReverts() public {
        vm.startPrank(OWNER);
        getV1Proxy().createPlan(
            PLAN_NAME_BASIC,
            PLAN_PRICE_BASIC,
            PLAN_DURATION_BASIC
        );
        vm.expectRevert(
            abi.encodeWithSelector(
                LibSubscription.DuplicatePlanName.selector,
                PLAN_NAME_BASIC
            )
        );
        getV1Proxy().createPlan(
            PLAN_NAME_BASIC,
            PLAN_PRICE_PRO,
            PLAN_DURATION_PRO
        ); // Same name, different data
        vm.stopPrank();
    }

    function test_V1_CreatePlan_InvalidDataReverts() public {
        vm.startPrank(OWNER);
        vm.expectRevert(
            abi.encodeWithSelector(LibSubscription.InvalidPlanData.selector)
        );
        getV1Proxy().createPlan("", PLAN_PRICE_BASIC, PLAN_DURATION_BASIC); // Empty name
        vm.expectRevert(
            abi.encodeWithSelector(LibSubscription.InvalidPlanData.selector)
        );
        getV1Proxy().createPlan("Test", 0, PLAN_DURATION_BASIC); // Zero price
        vm.expectRevert(
            abi.encodeWithSelector(LibSubscription.InvalidPlanData.selector)
        );
        getV1Proxy().createPlan("Test", PLAN_PRICE_BASIC, 0); // Zero duration
        vm.stopPrank();
    }

    function test_V1_DeactivatePlan() public {
        vm.startPrank(OWNER);
        uint8 planId = getV1Proxy().createPlan(
            PLAN_NAME_BASIC,
            PLAN_PRICE_BASIC,
            PLAN_DURATION_BASIC
        );
        assertTrue(getV1Proxy().planExists(planId));
        getV1Proxy().deactivatePlan(planId);
        assertFalse(getV1Proxy().planExists(planId));
        vm.stopPrank();
    }

    function test_V1_DeactivatePlan_NonExistentReverts() public {
        vm.startPrank(OWNER);
        vm.expectRevert(
            abi.encodeWithSelector(LibSubscription.InvalidPlanData.selector)
        );
        getV1Proxy().deactivatePlan(99); // Non-existent plan
        vm.stopPrank();
    }

    function test_V1_DeactivatePlan_AlreadyDeactivatedReverts() public {
        vm.startPrank(OWNER);
        uint8 planId = getV1Proxy().createPlan(
            PLAN_NAME_BASIC,
            PLAN_PRICE_BASIC,
            PLAN_DURATION_BASIC
        );
        getV1Proxy().deactivatePlan(planId);
        vm.expectRevert(
            abi.encodeWithSelector(LibSubscription.InvalidPlanData.selector)
        );
        getV1Proxy().deactivatePlan(planId);
        vm.stopPrank();
    }

    function test_V1_Subscribe_NewUser() public {
        vm.startPrank(OWNER);
        uint8 planId = getV1Proxy().createPlan(
            PLAN_NAME_BASIC,
            PLAN_PRICE_BASIC,
            PLAN_DURATION_BASIC
        );
        vm.stopPrank();

        vm.deal(USER1, PLAN_PRICE_BASIC); // Fund user1
        vm.startPrank(USER1);
        getV1Proxy().subscribe{value: PLAN_PRICE_BASIC}(planId);
        vm.stopPrank();

        assertTrue(getV1Proxy().isActiveSubscriber(USER1));
        (
            uint8 subPlanId,
            uint256 expiry,
            bool paused,
            bool isActive
        ) = getV1Proxy().getSubscriptionDetails(USER1); // Use V2's helper, but it would read V1's data
        assertEq(subPlanId, planId);
        assertEq(expiry, block.timestamp + PLAN_DURATION_BASIC);
        assertFalse(paused);
        assertTrue(isActive);
    }

    function test_V1_Subscribe_ExtendSubscription() public {
        vm.startPrank(OWNER);
        uint8 planId = getV1Proxy().createPlan(
            PLAN_NAME_BASIC,
            PLAN_PRICE_BASIC,
            PLAN_DURATION_BASIC
        );
        vm.stopPrank();

        vm.deal(USER1, PLAN_PRICE_BASIC * 2); // Fund user1 for two subscriptions
        vm.startPrank(USER1);
        getV1Proxy().subscribe{value: PLAN_PRICE_BASIC}(planId); // First subscription
        uint256 firstExpiry = block.timestamp + PLAN_DURATION_BASIC;

        vm.warp(block.timestamp + 10 days); // Advance time, still active

        getV1Proxy().subscribe{value: PLAN_PRICE_BASIC}(planId); // Extend subscription
        uint256 secondExpiry = firstExpiry + PLAN_DURATION_BASIC;
        vm.stopPrank();

        // Check the subscription details
        (
            uint8 subPlanId,
            uint256 expiry,
            bool paused,
            bool isActive
        ) = getV1Proxy().getSubscriptionDetails(USER1);
        assertEq(expiry, secondExpiry);
        assertTrue(isActive);
        assertFalse(paused, "New subscription should not be paused");
        assertEq(
            subPlanId,
            planId,
            "Subscription plan ID should match created plan"
        );
        assertGt(subPlanId, 0, "Plan ID should be greater than 0");
        assertGt(expiry, block.timestamp, "Expiry should be in the future");

        // Test that the subscription is properly stored
        assertTrue(
            getV1Proxy().isActiveSubscriber(USER1),
            "User should be an active subscriber"
        );

        // Test edge case: subscription should not be active for different user
        assertFalse(
            getV1Proxy().isActiveSubscriber(USER2),
            "Different user should not be active subscriber"
        );
    }

    function test_V1_Subscribe_InsufficientPaymentReverts() public {
        vm.startPrank(OWNER);
        uint8 planId = getV1Proxy().createPlan(
            PLAN_NAME_BASIC,
            PLAN_PRICE_BASIC,
            PLAN_DURATION_BASIC
        );
        vm.stopPrank();

        vm.deal(USER1, PLAN_PRICE_BASIC - 1); // Fund with less than required
        vm.startPrank(USER1);
        vm.expectRevert(
            abi.encodeWithSelector(
                SubscriptionLogicV1.InsufficentPayment.selector
            )
        );
        getV1Proxy().subscribe{value: PLAN_PRICE_BASIC - 1}(planId);
        vm.stopPrank();
    }

    function test_V1_WithdrawRefund_Overpayment() public {
        vm.startPrank(OWNER);
        uint8 planId = getV1Proxy().createPlan(
            PLAN_NAME_BASIC,
            PLAN_PRICE_BASIC,
            PLAN_DURATION_BASIC
        );
        vm.stopPrank();

        uint256 overpayment = 0.5 ether;
        vm.deal(USER1, PLAN_PRICE_BASIC + overpayment);

        vm.startPrank(USER1);
        getV1Proxy().subscribe{value: PLAN_PRICE_BASIC + overpayment}(planId);
        uint256 initialUserBalance = USER1.balance;
        uint256 initialContractBalance = address(proxy).balance;

        vm.expectEmit(true, true, false, true);
        emit SubscriptionLogicV1.RefundWithdrawn(USER1, overpayment);
        getV1Proxy().withdrawRefund();
        vm.stopPrank();

        assertEq(USER1.balance, initialUserBalance + overpayment);
        assertEq(address(proxy).balance, initialContractBalance - overpayment);

        // Try to withdraw again, should revert
        vm.startPrank(USER1);
        vm.expectRevert(
            abi.encodeWithSelector(
                SubscriptionLogicV1.NoRefundAvailable.selector
            )
        );
        getV1Proxy().withdrawRefund();
        vm.stopPrank();
    }

    function test_V1_WithdrawRefund_NoRefundReverts() public {
        vm.startPrank(USER1);
        vm.expectRevert(
            abi.encodeWithSelector(
                SubscriptionLogicV1.NoRefundAvailable.selector
            )
        );
        getV1Proxy().withdrawRefund();
        vm.stopPrank();
    }

    // --- Upgrade Tests ---

    function test_UpgradeToV2() public {
        vm.startPrank(OWNER);
        proxy.upgradeLogic(address(logicV2));
        vm.stopPrank();

        assertEq(proxy.getLogicContract(), address(logicV2));

        // Test V1 function through V2 logic (should still work)
        vm.startPrank(OWNER);
        uint8 planId = getV2Proxy().createPlan(
            PLAN_NAME_PRO,
            PLAN_PRICE_PRO,
            PLAN_DURATION_PRO
        );
        vm.stopPrank();
        assertEq(planId, 1); // Plan counter continues
        assertTrue(getV2Proxy().planExists(planId));
    }

    function test_UpgradeToV2_NonOwnerReverts() public {
        vm.startPrank(NON_OWNER);
        vm.expectRevert(
            abi.encodeWithSelector(
                LibSubscription.NotOwner.selector,
                NON_OWNER,
                OWNER
            )
        );
        proxy.upgradeLogic(address(logicV2));
        vm.stopPrank();
    }

    function test_UpgradeToV2_ZeroAddressReverts() public {
        vm.startPrank(OWNER);
        vm.expectRevert(
            abi.encodeWithSelector(
                LibSubscription.ZeroAddressNotAllowed.selector
            )
        );
        proxy.upgradeLogic(address(0));
        vm.stopPrank();
    }

    function test_UpgradeToV2_SameLogicContractReverts() public {
        vm.startPrank(OWNER);
        vm.expectRevert(
            abi.encodeWithSelector(LibSubscription.SameLogicContract.selector)
        );
        proxy.upgradeLogic(address(logicV1)); // Try to upgrade to current logic
        vm.stopPrank();
    }

    // --- Tests for V2 Functionality ---

    function test_V2_PauseAndUnpauseSubscription() public {
        // Setup initial subscription
        vm.startPrank(OWNER);
        uint8 planId = getV1Proxy().createPlan(
            PLAN_NAME_BASIC,
            PLAN_PRICE_BASIC,
            PLAN_DURATION_BASIC
        );
        vm.stopPrank();

        vm.deal(USER1, PLAN_PRICE_BASIC);
        vm.startPrank(USER1);
        getV1Proxy().subscribe{value: PLAN_PRICE_BASIC}(planId);
        vm.stopPrank();

        // Upgrade to V2
        vm.startPrank(OWNER);
        proxy.upgradeLogic(address(logicV2));
        vm.stopPrank();

        // Check initial state (active)
        (, , , bool isActiveBeforePause) = getV2Proxy().getSubscriptionDetails(
            USER1
        );
        assertTrue(isActiveBeforePause);

        // Pause subscription as owner
        vm.startPrank(OWNER);
        vm.expectEmit(true, true, false, true);
        emit SubscriptionLogicV2.SubscriptionPaused(USER1, OWNER);
        getV2Proxy().pauseUserSubscription(USER1);
        vm.stopPrank();

        // Verify paused state
        (, , bool pausedAfterPause, bool isActiveAfterPause) = getV2Proxy()
            .getSubscriptionDetails(USER1);
        assertTrue(pausedAfterPause);
        assertFalse(isActiveAfterPause); // Should be inactive because it's paused

        // Try to pause again, should revert
        vm.startPrank(OWNER);
        vm.expectRevert(
            abi.encodeWithSelector(
                SubscriptionLogicV2.SubscriptionAlreadyPaused.selector
            )
        );
        getV2Proxy().pauseUserSubscription(USER1);
        vm.stopPrank();

        // Unpause subscription as owner
        vm.startPrank(OWNER);
        vm.expectEmit(true, true, false, true);
        emit SubscriptionLogicV2.SubscriptionUnpaused(USER1, OWNER);
        getV2Proxy().unpauseUserSubscription(USER1);
        vm.stopPrank();

        // Verify unpaused state
        (, , bool pausedAfterUnpause, bool isActiveAfterUnpause) = getV2Proxy()
            .getSubscriptionDetails(USER1);
        assertFalse(pausedAfterUnpause);
        assertTrue(isActiveAfterUnpause); // Should be active again

        // Try to unpause again, should revert
        vm.startPrank(OWNER);
        vm.expectRevert(
            abi.encodeWithSelector(
                SubscriptionLogicV2.SubscriptionNotPaused.selector
            )
        );
        getV2Proxy().unpauseUserSubscription(USER1);
        vm.stopPrank();
    }

    function test_V2_PauseAndUnpauseSubscription_NonOwnerReverts() public {
        // Setup subscription
        vm.startPrank(OWNER);
        uint8 planId = getV1Proxy().createPlan(
            PLAN_NAME_BASIC,
            PLAN_PRICE_BASIC,
            PLAN_DURATION_BASIC
        );
        vm.stopPrank();
        vm.deal(USER1, PLAN_PRICE_BASIC);
        vm.startPrank(USER1);
        getV1Proxy().subscribe{value: PLAN_PRICE_BASIC}(planId);
        vm.stopPrank();

        // Upgrade to V2
        vm.startPrank(OWNER);
        proxy.upgradeLogic(address(logicV2));
        vm.stopPrank();

        // Try to pause as non-owner
        vm.startPrank(NON_OWNER);
        vm.expectRevert(
            abi.encodeWithSelector(
                LibSubscription.NotOwner.selector,
                NON_OWNER,
                OWNER
            )
        );
        getV2Proxy().pauseUserSubscription(USER1);
        vm.stopPrank();

        // Try to unpause as non-owner
        vm.startPrank(NON_OWNER);
        vm.expectRevert(
            abi.encodeWithSelector(
                LibSubscription.NotOwner.selector,
                NON_OWNER,
                OWNER
            )
        );
        getV2Proxy().unpauseUserSubscription(USER1);
        vm.stopPrank();
    }

    function test_V2_PauseUnpause_UserNotSubscribedReverts() public {
        // Upgrade to V2 first
        vm.startPrank(OWNER);
        proxy.upgradeLogic(address(logicV2));
        vm.stopPrank();

        // No subscription for USER1
        vm.startPrank(OWNER);
        vm.expectRevert(
            abi.encodeWithSelector(
                SubscriptionLogicV2.UserNotSubscribed.selector
            )
        );
        getV2Proxy().pauseUserSubscription(USER1);
        vm.expectRevert(
            abi.encodeWithSelector(
                SubscriptionLogicV2.UserNotSubscribed.selector
            )
        );
        getV2Proxy().unpauseUserSubscription(USER1);
        vm.stopPrank();

        // Create a subscription but let it expire
        vm.startPrank(OWNER);
        uint8 planId = getV2Proxy().createPlan(
            PLAN_NAME_BASIC,
            PLAN_PRICE_BASIC,
            1
        ); // 1 second duration
        vm.stopPrank();

        vm.deal(USER1, PLAN_PRICE_BASIC);
        vm.startPrank(USER1);
        getV2Proxy().subscribe{value: PLAN_PRICE_BASIC}(planId);
        vm.stopPrank();

        vm.warp(block.timestamp + 2 days); // Ensure it's expired

        vm.startPrank(OWNER);
        vm.expectRevert(
            abi.encodeWithSelector(
                SubscriptionLogicV2.UserNotSubscribed.selector
            )
        );
        getV2Proxy().pauseUserSubscription(USER1);
        vm.expectRevert(
            abi.encodeWithSelector(
                SubscriptionLogicV2.UserNotSubscribed.selector
            )
        );
        getV2Proxy().unpauseUserSubscription(USER1);
        vm.stopPrank();
    }

    function test_V2_GetSubscriptionDetails() public {
        vm.startPrank(OWNER);
        uint8 planId = getV1Proxy().createPlan(
            PLAN_NAME_BASIC,
            PLAN_PRICE_BASIC,
            PLAN_DURATION_BASIC
        );
        proxy.upgradeLogic(address(logicV2));
        vm.stopPrank();

        vm.deal(USER1, PLAN_PRICE_BASIC);
        vm.startPrank(USER1);
        uint256 subscribeTime = block.timestamp;
        getV2Proxy().subscribe{value: PLAN_PRICE_BASIC}(planId);
        vm.stopPrank();

        // Test active subscription details
        (
            uint8 subPlanId,
            uint256 expiry,
            bool paused,
            bool isActive
        ) = getV2Proxy().getSubscriptionDetails(USER1);

        // Enhanced assertions
        assertEq(subPlanId, planId, "Plan ID should match");
        assertGt(subPlanId, 0, "Plan ID should be positive");
        assertEq(
            expiry,
            subscribeTime + PLAN_DURATION_BASIC,
            "Expiry calculation should be correct"
        );
        assertGt(expiry, block.timestamp, "Subscription should not be expired");
        assertFalse(paused, "Subscription should not be paused");
        assertTrue(isActive);

        // Test consistency with other functions
        assertTrue(
            getV2Proxy().isActiveSubscriber(USER1),
            "isActiveSubscriber should return true"
        );
        assertTrue(getV2Proxy().planExists(planId), "Plan should still exist");

        // Test for user without subscription (existing test is good)
        (
            uint8 noSubPlanId,
            uint256 noSubExpiry,
            bool noSubPaused,
            bool noSubIsActive
        ) = getV2Proxy().getSubscriptionDetails(USER2);
        assertEq(noSubPlanId, 0, "No subscription should have plan ID 0");
        assertEq(noSubExpiry, 0, "No subscription should have expiry 0");
        assertFalse(noSubPaused, "No subscription should not be paused");
        assertFalse(noSubIsActive, "No subscription should not be active");

        // Additional test: expired subscription
        vm.warp(expiry + 1); // Move past expiration
        (
            uint8 expiredPlanId,
            uint256 expiredExpiry,
            bool expiredPaused,
            bool expiredIsActive
        ) = getV2Proxy().getSubscriptionDetails(USER1);
        assertEq(
            expiredPlanId,
            planId,
            "Expired subscription should keep plan ID"
        );
        assertEq(
            expiredExpiry,
            expiry,
            "Expired subscription should keep original expiry"
        );
        assertFalse(
            expiredPaused,
            "Expired subscription pause state should remain"
        );
        assertFalse(
            expiredIsActive,
            "Expired subscription should not be active"
        );
        assertFalse(
            getV2Proxy().isActiveSubscriber(USER1),
            "Expired user should not be active subscriber"
        );
    }
}
