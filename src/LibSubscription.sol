// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

library LibSubscription {
    error NotOwner(address _caller, address _owner);
    error ZeroAddressNotAllowed();
    error PlanAlreadyExists(uint8 planId);
    error SameLogicContract();
    error AlreadyInitialized();
    error NotFound(string name, uint8 planId);
    error InvalidPlanData();
    error DuplicatePlanName(string _name);
    error MaxPlansReached();

    error PlanAlreadyActive();
    error PlanAlreadyDeactivated();

    bytes32 constant APP_STORAGE_POSITION = keccak256("diamond.app.storage");

    struct Subscription {
        uint8 planId;
        uint256 expiry;
        bool paused;
    }

    struct Plan {
        string name;
        uint256 price;
        uint256 duration;
    }

    event OwnershipTransferred(address indexed previousOwner, address indexed owner);
    event PlanAdded(uint8 indexed planId, string name);
    event LogicContractUpgraded(address indexed oldLogic, address newLogic);
    event PlanCreated(uint8 indexed planId, string name, uint256 price, uint256 duration);
    event PlanDeactivated(uint8 planId);

    struct AppStorage {
        address logicContract;
        address owner;
        uint8 planCounter;
        mapping(address => Subscription) subscriptions;
        mapping(uint8 => Plan) plans;
        mapping(uint8 => bool) planActive;
        mapping(bytes32 => bool) planNameExists;
        // to store excess funds payed
        mapping(address => uint256) refundBalances;
    }

    function appStorage() internal pure returns (AppStorage storage ds) {
        bytes32 position = APP_STORAGE_POSITION;
        assembly {
            ds.slot := position
        }
    }

    function initializeContract(address _owner, address _logicContract) internal {
        AppStorage storage ds = appStorage();
        if (ds.owner != address(0)) revert AlreadyInitialized();
        ds.owner = _owner;
        ds.logicContract = _logicContract;
    }

    function owner() internal view returns (address) {
        return appStorage().owner;
    }

    function setOwner(address _owner) internal {
        AppStorage storage ds = appStorage();
        address previousOwner = ds.owner;
        ds.owner = _owner;

        emit OwnershipTransferred(previousOwner, _owner);
    }

    function planName(uint8 planId) internal view returns (string memory name) {
        return appStorage().plans[planId].name;
    }

    function enforceIsContractOwner() internal view {
        if (msg.sender != appStorage().owner) revert NotOwner(msg.sender, appStorage().owner);
    }

    function upgradeTo(address _newLogic) internal {
        if (_newLogic == address(0)) revert ZeroAddressNotAllowed();

        enforceIsContractOwner();
        AppStorage storage ds = appStorage();
        if (_newLogic == ds.logicContract) revert SameLogicContract();

        address previousLogic = ds.logicContract;
        ds.logicContract = _newLogic;

        emit LogicContractUpgraded(previousLogic, _newLogic);
    }

    function logicContract() internal view returns (address) {
        return appStorage().logicContract;
    }

    function createPlan(string memory _name, uint256 _price, uint256 _duration) internal returns (uint8 planId) {
        enforceIsContractOwner();
        if (bytes(_name).length == 0) revert InvalidPlanData();
        if (_price == 0) revert InvalidPlanData();
        if (_duration == 0) revert InvalidPlanData();

        AppStorage storage ds = appStorage();
        if (ds.planCounter >= 255) revert MaxPlansReached();

        // Check for duplicate name (O(1) lookup)
        bytes32 nameHash = keccak256(bytes(_name));
        if (ds.planNameExists[nameHash]) revert DuplicatePlanName(_name);

        ds.planCounter++;
        planId = ds.planCounter;

        ds.plans[planId] = Plan({name: _name, price: _price, duration: _duration});
        ds.planActive[planId] = true;
        ds.planNameExists[nameHash] = true; // Mark name as taken

        emit PlanCreated(planId, _name, _price, _duration);
    }

    function planExists(uint8 _planId) internal view returns (bool) {
        AppStorage storage ds = appStorage();
        return _planId > 0 && _planId <= ds.planCounter && ds.planActive[_planId];
    }

    function deactivatePlan(uint8 _planId) internal {
        enforceIsContractOwner();
        AppStorage storage ds = appStorage();

        if (!planExists(_planId)) revert InvalidPlanData();
        if (!ds.planActive[_planId]) revert PlanAlreadyDeactivated();

        // Free up the name for reuse
        bytes32 nameHash = keccak256(bytes(ds.plans[_planId].name));
        ds.planNameExists[nameHash] = false;

        ds.planActive[_planId] = false;
        emit PlanDeactivated(_planId);
    }

    function getActivePlansCount() internal view returns (uint8 activeCount) {
        AppStorage storage ds = appStorage();
        for (uint8 i = 1; i <= ds.planCounter; i++) {
            if (ds.planActive[i]) {
                activeCount++;
            }
        }
    }

    function getOnlyActivePlans() internal view returns (Plan[] memory activePlans, uint8[] memory planIds) {
        AppStorage storage ds = appStorage();
        uint8 activeCount = getActivePlansCount();

        activePlans = new Plan[](activeCount);
        planIds = new uint8[](activeCount);

        uint8 index = 0;
        for (uint8 i = 1; i <= ds.planCounter; i++) {
            if (ds.planActive[i]) {
                activePlans[index] = ds.plans[i];
                planIds[index] = i;
                index++;
            }
        }
    }
}
