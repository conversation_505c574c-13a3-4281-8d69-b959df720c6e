// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import {LibSubscription} from "src/LibSubscription.sol";

contract SubscriptionLogicV2 {
    error PlanNotFound();
    error InsufficentPayment();
    error NoRefundAvailable();
    error UserNotSubscribed();
    error SubscriptionAlreadyPaused();
    error SubscriptionNotPaused();

    event Subscribed(address indexed userAddress, uint256 planId);
    event RefundWithdrawn(address indexed userAddress, uint256 amount);
    event SubscriptionPaused(address indexed userAddress, address indexed admin);
    event SubscriptionUnpaused(address indexed userAddress, address indexed admin);

    // All V1 functions remain unchanged
    function createPlan(string memory name, uint256 price, uint256 duration) external returns (uint8) {
        return LibSubscription.createPlan(name, price, duration);
    }

    function deactivatePlan(uint8 planId) external {
        LibSubscription.deactivatePlan(planId);
    }

    function getActivePlansCount() external view returns (uint8) {
        return LibSubscription.getActivePlansCount();
    }

    function getOnlyActivePlans() external view returns (LibSubscription.Plan[] memory, uint8[] memory) {
        return LibSubscription.getOnlyActivePlans();
    }

    function subscribe(uint8 planId) external payable {
        if (LibSubscription.planExists(planId) == false) revert PlanNotFound();

        LibSubscription.AppStorage storage ds = LibSubscription.appStorage();
        LibSubscription.Plan memory plan = ds.plans[planId];

        if (msg.value < plan.price) revert InsufficentPayment();

        bool hasActiveSubscription = block.timestamp < ds.subscriptions[msg.sender].expiry;
        if (hasActiveSubscription) {
            ds.subscriptions[msg.sender].expiry += plan.duration;
        } else {
            ds.subscriptions[msg.sender] =
                LibSubscription.Subscription({planId: planId, expiry: block.timestamp + plan.duration, paused: false});
        }

        if (msg.value > plan.price) {
            ds.refundBalances[msg.sender] += msg.value - plan.price;
        }

        emit Subscribed(msg.sender, planId);
    }

    function withdrawRefund() external {
        LibSubscription.AppStorage storage ds = LibSubscription.appStorage();
        uint256 amount = ds.refundBalances[msg.sender];

        if (amount == 0) revert NoRefundAvailable();

        ds.refundBalances[msg.sender] = 0;
        payable(msg.sender).transfer(amount);

        emit RefundWithdrawn(msg.sender, amount);
    }

    function planExists(uint8 _planId) public view returns (bool) {
        return LibSubscription.planExists(_planId);
    }

    function isActiveSubscriber(address user) external view returns (bool) {
        LibSubscription.AppStorage storage ds = LibSubscription.appStorage();
        LibSubscription.Subscription memory subscription = ds.subscriptions[user];

        return block.timestamp < subscription.expiry && !subscription.paused;
    }

    // NEW V2 FUNCTIONS: Admin pause/unpause
    function pauseUserSubscription(address user) external {
        LibSubscription.enforceIsContractOwner();

        LibSubscription.AppStorage storage ds = LibSubscription.appStorage();
        LibSubscription.Subscription storage subscription = ds.subscriptions[user];

        // Check if user has valid subscription
        if (block.timestamp >= subscription.expiry) revert UserNotSubscribed();
        if (subscription.paused) revert SubscriptionAlreadyPaused();

        subscription.paused = true;
        emit SubscriptionPaused(user, msg.sender);
    }

    function unpauseUserSubscription(address user) external {
        LibSubscription.enforceIsContractOwner();

        LibSubscription.AppStorage storage ds = LibSubscription.appStorage();
        LibSubscription.Subscription storage subscription = ds.subscriptions[user];

        // Check if user has valid subscription
        if (block.timestamp >= subscription.expiry) revert UserNotSubscribed();
        if (!subscription.paused) revert SubscriptionNotPaused();

        subscription.paused = false;
        emit SubscriptionUnpaused(user, msg.sender);
    }

    // Additional helper function
    function getSubscriptionDetails(address user)
        external
        view
        returns (uint8 planId, uint256 expiry, bool paused, bool isActive)
    {
        LibSubscription.AppStorage storage ds = LibSubscription.appStorage();
        LibSubscription.Subscription memory subscription = ds.subscriptions[user];

        return (
            subscription.planId,
            subscription.expiry,
            subscription.paused,
            block.timestamp < subscription.expiry && !subscription.paused
        );
    }
}
