// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import {LibSubscription} from "./LibSubscription.sol";

contract Subscription {
    constructor(address _logicContract) {
        LibSubscription.initializeContract(msg.sender, _logicContract);
    }

    function upgradeLogic(address _newLogic) external {
        LibSubscription.upgradeTo(_newLogic);
    }

    function proxyOwner() external view returns (address) {
        return LibSubscription.owner();
    }

    function transferOwnership(address _newOwner) external {
        LibSubscription.enforceIsContractOwner();
        LibSubscription.setOwner(_newOwner);
    }

    function getLogicContract() external view returns (address) {
        return LibSubscription.logicContract();
    }

    function planExists(uint8 _planId) internal view returns (bool) {
        return LibSubscription.planExists(_planId);
    }

    fallback() external payable {
        address logicContract = LibSubscription.logicContract();

        assembly {
            // copy function selector and any arguments
            calldatacopy(0, 0, calldatasize())
            // execute function call using the logicContract
            let result := delegatecall(gas(), logicContract, 0, calldatasize(), 0, 0)
            // get any return value
            returndatacopy(0, 0, returndatasize())
            // return any return value or error back to the caller
            switch result
            case 0 { revert(0, returndatasize()) }
            default { return(0, returndatasize()) }
        }
    }

    receive() external payable {}
}
