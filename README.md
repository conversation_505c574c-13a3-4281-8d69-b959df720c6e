# Diamond Storage Proxy Pattern

> A practical implementation demonstrating how Diamond Storage can enhance any proxy contract architecture, not just EIP-2535 Diamonds.

## 🚀 Overview

This project showcases the **Diamond Storage pattern** as a versatile storage management technique for upgradeable proxy contracts. While Diamond Storage was popularized by the EIP-2535 Diamond standard, this implementation proves it can supercharge any proxy pattern with safe, gas-efficient, collision-free storage.

### Key Features

- ✅ **Diamond Storage Pattern** - Deterministic storage slots using keccak256 hashes
- ✅ **Upgradeable Proxy** - Simple proxy contract with logic contract swapping
- ✅ **State Preservation** - Complete state maintained across upgrades
- ✅ **Gas Efficient** - 3-4x cheaper than external storage contracts
- ✅ **Atomic Operations** - No risk of partial state corruption
- ✅ **Comprehensive Testing** - 95%+ test coverage with edge cases

## 📖 Background

**Read the full technical breakdown:** [Diamond Storage Beyond Diamonds: A Practical Guide to Upgradeable Proxy Contracts](https://joseph-anyaegbunam.dev/blog/diamondStorage)

Most developers think Diamond Storage is exclusive to Diamond contracts. This project demonstrates how to leverage the same pattern in simple proxy architectures for reliable, upgradeable storage management.

## 🏗️ Architecture

```
┌─────────────────┐    delegatecall    ┌─────────────────────┐
│                 │ ─────────────────> │                     │
│  Subscription   │                    │ SubscriptionLogicV1 │
│  (Proxy)        │ <───────────────── │ (Logic Contract)    │
│                 │      return        │                     │
└─────────────────┘                    └─────────────────────┘
         │                                       │
         │                                       │
         ▼                                       ▼
┌─────────────────┐                    ┌─────────────────────┐
│                 │ ◄──────────────────│                     │
│  LibSubscription│                    │ SubscriptionLogicV2 │
│ (Diamond Storage│                    │ (Upgraded Logic)    │
│   & Logic)      │                    │                     │
└─────────────────┘                    └─────────────────────┘

Storage Location: keccak256("myproject.subscription.app.storage")
```

### Components

- **`Subscription.sol`** - Proxy contract with fallback delegation
- **`LibSubscription.sol`** - Diamond Storage library with business logic
- **`SubscriptionLogicV1.sol`** - Initial logic implementation
- **`SubscriptionLogicV2.sol`** - Upgraded logic with pause/unpause features

## 🛠️ Installation & Setup

### Prerequisites

- [Foundry](https://book.getfoundry.sh/getting-started/installation)
- Git

### Clone & Install

```bash
git clone https://github.com/Chukwuemekamusic/diamond-storage-subscription-proxy.git
cd diamond-storage-subscription-proxy
forge install
```

### Build

```bash
forge build
```

## 🧪 Testing

### Run All Tests

```bash
forge test
```

### Run Specific Test Categories

```bash
# Core functionality tests
forge test --match-contract SubscriptionTest

# Diamond Storage pattern validation
forge test --match-contract SubscriptionStorageTest

# Verbose output with gas reports
forge test -vvv --gas-report
```

### Test Coverage

```bash
forge coverage
```

## 📊 Gas Comparison

Diamond Storage vs External Storage Contract:

| Operation             | Diamond Storage | External Storage | Savings |
| --------------------- | --------------- | ---------------- | ------- |
| Single storage read   | ~200 gas        | ~2,100 gas       | **90%** |
| Complex state update  | ~10,020 gas     | ~42,000 gas      | **76%** |
| Subscription creation | ~85,000 gas     | ~165,000 gas     | **48%** |

_Run `forge test --gas-report` for detailed gas analysis_

## 🚀 Quick Start

### 1. Deploy Contracts

```bash
forge script script/DeploySubscription.s.sol --rpc-url $RPC_URL --private-key $PRIVATE_KEY
```

### 2. Interact with the Proxy

```solidity
// Create a subscription plan (owner only)
SubscriptionLogicV1(proxyAddress).createPlan("Basic Plan", 1 ether, 30 days);

// Subscribe to a plan
SubscriptionLogicV1(proxyAddress).subscribe{value: 1 ether}(1);

// Check subscription status
bool isActive = SubscriptionLogicV1(proxyAddress).isActiveSubscriber(userAddress);
```

### 3. Upgrade to V2

```solidity
// Deploy new logic contract
SubscriptionLogicV2 newLogic = new SubscriptionLogicV2();

// Upgrade proxy (owner only)
Subscription(proxyAddress).upgradeLogic(address(newLogic));

// Use new V2 features
SubscriptionLogicV2(proxyAddress).pauseUserSubscription(userAddress);
```

## 📝 Usage Examples

### Creating Plans

```solidity
// Connect to proxy as V1
SubscriptionLogicV1 subscription = SubscriptionLogicV1(proxyAddress);

// Create different subscription tiers
uint8 basicPlan = subscription.createPlan("Basic", 0.1 ether, 30 days);
uint8 proPlan = subscription.createPlan("Pro", 0.3 ether, 30 days);
uint8 enterprisePlan = subscription.createPlan("Enterprise", 1 ether, 30 days);
```

### User Subscriptions

```solidity
// Subscribe to a plan
subscription.subscribe{value: 0.1 ether}(basicPlan);

// Check subscription details
(uint8 planId, uint256 expiry, bool paused, bool isActive) =
    subscription.getSubscriptionDetails(msg.sender);

// Withdraw overpayment refunds
subscription.withdrawRefund();
```

### V2 Admin Features

```solidity
// Upgrade to V2 first
proxy.upgradeLogic(address(logicV2));

SubscriptionLogicV2 subscriptionV2 = SubscriptionLogicV2(proxyAddress);

// Pause user subscription
subscriptionV2.pauseUserSubscription(userAddress);

// Unpause user subscription
subscriptionV2.unpauseUserSubscription(userAddress);
```

## 🔍 Key Concepts Demonstrated

### 1. Diamond Storage Pattern

```solidity
library LibSubscription {
    bytes32 constant APP_STORAGE_POSITION = keccak256("myproject.subscription.app.storage");

    struct AppStorage {
        address logicContract;
        address owner;
        mapping(address => Subscription) subscriptions;
        mapping(uint8 => Plan) plans;
        // ... more storage
    }

    function appStorage() internal pure returns (AppStorage storage ds) {
        bytes32 position = APP_STORAGE_POSITION;
        assembly {
            ds.slot := position
        }
    }
}
```

### 2. Collision-Free Upgrades

- **Deterministic storage slots** prevent storage layout conflicts
- **Struct-based organization** keeps related data together
- **Append-only additions** ensure safe storage expansion

### 3. State Preservation

All user data survives upgrades:

- Subscription plans and pricing
- User subscription details and expiry dates
- Refund balances and payment history
- Admin settings and access controls

## 🏆 Why This Pattern Works

### vs External Storage Contracts

| Aspect                | Diamond Storage          | External Storage           |
| --------------------- | ------------------------ | -------------------------- |
| **Gas Efficiency**    | ~200 gas per access      | ~2,100+ gas per call       |
| **Atomic Operations** | ✅ Single transaction    | ❌ Risk of partial updates |
| **Code Complexity**   | ✅ Clean library pattern | ❌ Complex getter/setters  |
| **Access Control**    | ✅ Integrated            | ❌ Separate authorization  |

### vs Traditional Proxy Patterns

- **No storage collisions** from compiler changes
- **Flexible storage expansion** without layout concerns
- **Library-based organization** for clean separation of concerns
- **Gas efficiency** comparable to direct storage access

## 🛡️ Security Considerations

### Best Practices Implemented

- ✅ **Access control** with `enforceIsContractOwner()`
- ✅ **Input validation** for all user inputs
- ✅ **Reentrancy protection** with state-before-external-calls
- ✅ **Safe arithmetic** with explicit overflow checks
- ✅ **Event emission** for transparency

### Storage Safety

- ✅ **Unique storage slots** prevent collisions
- ✅ **Append-only struct growth** maintains compatibility
- ✅ **No storage gaps** in struct layout
- ✅ **Immutable storage position** hash

## 📚 Further Reading

- [EIP-2535 Diamond Standard](https://eips.ethereum.org/EIPS/eip-2535)
- [OpenZeppelin Proxy Patterns](https://docs.openzeppelin.com/contracts/4.x/api/proxy)
- [Foundry Book](https://book.getfoundry.sh/)
- [Solidity Documentation](https://docs.soliditylang.org/)

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

### Development Setup

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Run tests (`forge test`)
4. Commit your changes (`git commit -m 'Add amazing feature'`)
5. Push to the branch (`git push origin feature/amazing-feature`)
6. Open a Pull Request

### Test Requirements

- All tests must pass
- Maintain >90% test coverage
- Add tests for new functionality
- Follow existing code style

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👨‍💻 Author

**Joseph Anyaegbunam**

- Portfolio: [joseph-anyaegbunam.dev](https://joseph-anyaegbunam.dev)
- GitHub: [@Chukwuemekamusic](https://github.com/Chukwuemekamusic)
- LinkedIn: [Joseph Anyaegbunam](https://linkedin.com/in/joseph-anyaegbunam-b1a430253)
- Article: [Diamond Storage Beyond Diamonds](ARTICLE_LINK)

## 🙏 Acknowledgments

- Nick Mudge, creator of the [Diamond Standard (EIP-2535)](https://eips.ethereum.org/EIPS/eip-2535), for pioneering the storage pattern
- [OpenZeppelin](https://openzeppelin.com/) for proxy contract inspiration
- [Foundry](https://github.com/foundry-rs/foundry) for excellent development tooling

---

⭐ **If this project helped you understand Diamond Storage patterns, please consider giving it a star!**
